const { sendTemplateEmail, sendCustomEmail, EMAIL_TEMPLATES } = require('../config/sendgrid');
const Lead = require('../models/Lead');

// Get available email templates
const getEmailTemplates = async (req, res) => {
  try {
    const templates = Object.keys(EMAIL_TEMPLATES).map(key => ({
      id: key,
      title: key.charAt(0).toUpperCase() + key.slice(1).toLowerCase().replace('_', ' '),
      subject: EMAIL_TEMPLATES[key].subject,
      type: key
    }));

    // Add custom template option
    templates.push({
      id: 'CUSTOM',
      title: 'Custom Email',
      subject: '',
      type: 'CUSTOM'
    });

    res.json({
      success: true,
      data: {
        templates
      },
      message: 'Email templates retrieved successfully'
    });

  } catch (error) {
    console.error('❌ Error fetching email templates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch email templates',
      error: error.message
    });
  }
};

// Send template email to lead
const sendTemplateEmailToLead = async (req, res) => {
  try {
    const { leadId, templateType, customSubject } = req.body;

    console.log('📧 Sending template email:', {
      leadId,
      templateType,
      customSubject
    });

    // Validate required fields
    if (!leadId || !templateType) {
      return res.status(400).json({
        success: false,
        message: 'Lead ID and template type are required'
      });
    }

    // Get lead details
    const lead = await Lead.findById(leadId);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    // Validate email address
    if (!lead.email) {
      return res.status(400).json({
        success: false,
        message: 'Lead does not have an email address'
      });
    }

    // Validate template type
    if (!EMAIL_TEMPLATES[templateType]) {
      return res.status(400).json({
        success: false,
        message: `Invalid template type: ${templateType}`
      });
    }

    console.log('📧 Sending template email to:', {
      leadName: lead.title,
      leadEmail: lead.email,
      templateType: templateType
    });

    // Send template email
    const result = await sendTemplateEmail({
      to: lead.email,
      templateType: templateType,
      leadName: lead.title,
      customSubject: customSubject
    });

    console.log('✅ Template email sent successfully:', result.messageId);

    res.json({
      success: true,
      data: {
        messageId: result.messageId,
        leadId: leadId,
        leadName: lead.title,
        leadEmail: lead.email,
        templateType: templateType,
        subject: customSubject || EMAIL_TEMPLATES[templateType].subject,
        sentAt: new Date()
      },
      message: 'Template email sent successfully'
    });

  } catch (error) {
    console.error('❌ Error sending template email:', error);
    
    // Handle specific SendGrid errors
    if (error.code === 400) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email configuration or recipient email',
        error: error.message
      });
    }
    
    if (error.code === 401) {
      return res.status(500).json({
        success: false,
        message: 'SendGrid authentication failed. Please check API key.',
        error: 'Invalid SendGrid API key'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to send template email',
      error: error.message
    });
  }
};

// Send custom email to lead
const sendCustomEmailToLead = async (req, res) => {
  try {
    const { leadId, subject, message } = req.body;

    console.log('📧 Sending custom email:', {
      leadId,
      subject,
      messageLength: message?.length
    });

    // Validate required fields
    if (!leadId || !subject || !message) {
      return res.status(400).json({
        success: false,
        message: 'Lead ID, subject, and message are required'
      });
    }

    // Get lead details
    const lead = await Lead.findById(leadId);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    // Validate email address
    if (!lead.email) {
      return res.status(400).json({
        success: false,
        message: 'Lead does not have an email address'
      });
    }

    console.log('📧 Sending custom email to:', {
      leadName: lead.title,
      leadEmail: lead.email,
      subject: subject
    });

    // Send custom email
    const result = await sendCustomEmail({
      to: lead.email,
      subject: subject,
      message: message,
      leadName: lead.title
    });

    console.log('✅ Custom email sent successfully:', result.messageId);

    res.json({
      success: true,
      data: {
        messageId: result.messageId,
        leadId: leadId,
        leadName: lead.title,
        leadEmail: lead.email,
        subject: subject,
        message: message,
        sentAt: new Date()
      },
      message: 'Custom email sent successfully'
    });

  } catch (error) {
    console.error('❌ Error sending custom email:', error);
    
    // Handle specific SendGrid errors
    if (error.code === 400) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email configuration or recipient email',
        error: error.message
      });
    }
    
    if (error.code === 401) {
      return res.status(500).json({
        success: false,
        message: 'SendGrid authentication failed. Please check API key.',
        error: 'Invalid SendGrid API key'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to send custom email',
      error: error.message
    });
  }
};

// Get email sending status/history for a lead
const getEmailHistory = async (req, res) => {
  try {
    const { leadId } = req.params;

    if (!leadId) {
      return res.status(400).json({
        success: false,
        message: 'Lead ID is required'
      });
    }

    // Get lead details
    const lead = await Lead.findById(leadId);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    // For now, return lead email info
    // In a production app, you'd store email history in a separate collection
    res.json({
      success: true,
      data: {
        leadId: leadId,
        leadName: lead.title,
        leadEmail: lead.email,
        hasEmail: !!lead.email,
        // In the future, you can add email history here
        emailHistory: [],
        stats: {
          totalSent: 0,
          lastSent: null
        }
      },
      message: 'Email history retrieved successfully'
    });

  } catch (error) {
    console.error('❌ Error fetching email history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch email history',
      error: error.message
    });
  }
};

// Test SendGrid configuration
const testEmailConfiguration = async (req, res) => {
  try {
    const { testEmail } = req.body;

    if (!testEmail) {
      return res.status(400).json({
        success: false,
        message: 'Test email address is required'
      });
    }

    console.log('🧪 Testing SendGrid configuration with:', testEmail);

    // Send a test email
    const result = await sendCustomEmail({
      to: testEmail,
      subject: 'SendGrid Configuration Test',
      message: 'This is a test email to verify that your SendGrid configuration is working correctly.\n\nIf you receive this email, your setup is successful!',
      leadName: 'Test User'
    });

    console.log('✅ Test email sent successfully:', result.messageId);

    res.json({
      success: true,
      data: {
        messageId: result.messageId,
        testEmail: testEmail,
        sentAt: new Date()
      },
      message: 'Test email sent successfully'
    });

  } catch (error) {
    console.error('❌ SendGrid configuration test failed:', error);
    
    let errorMessage = 'SendGrid configuration test failed';
    
    if (error.message.includes('API key not configured')) {
      errorMessage = 'SendGrid API key is not configured. Please set SENDGRID_API_KEY environment variable.';
    } else if (error.code === 401) {
      errorMessage = 'Invalid SendGrid API key. Please check your SENDGRID_API_KEY environment variable.';
    } else if (error.code === 400) {
      errorMessage = 'Invalid email address or SendGrid configuration.';
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
      error: error.message
    });
  }
};

module.exports = {
  getEmailTemplates,
  sendTemplateEmailToLead,
  sendCustomEmailToLead,
  getEmailHistory,
  testEmailConfiguration
}; 