const dotenv = require('dotenv');
dotenv.config();

// Import our Twilio configuration
const { initiateCall, TWILIO_CONFIG } = require('./config/twilio');

console.log('🔍 Testing Call Initiation\n');

// Test configuration
const TEST_PHONE_NUMBER = '+**********'; // Replace with a valid test number
const AGENT_NAME = 'Test Agent';
const WEBHOOK_BASE_URL = process.env.WEBHOOK_BASE_URL || 'https://your-ngrok-url.ngrok.io';

console.log('📋 Test Configuration:');
console.log('   Test Phone Number:', TEST_PHONE_NUMBER);
console.log('   Agent Name:', AGENT_NAME);
console.log('   Webhook Base URL:', WEBHOOK_BASE_URL);
console.log('   Twilio Phone From:', TWILIO_CONFIG.PHONE_FROM);
console.log('');

async function testCallInitiation() {
  try {
    console.log('🚀 Starting call initiation test...');
    
    const result = await initiateCall(
      TEST_PHONE_NUMBER,
      TWILIO_CONFIG.PHONE_FROM,
      AGENT_NAME,
      WEBHOOK_BASE_URL
    );
    
    if (result.success) {
      console.log('✅ Call initiated successfully!');
      console.log('   Call SID:', result.callSid);
      console.log('   Status:', result.status);
      console.log('   To:', result.to);
      console.log('   From:', result.from);
    } else {
      console.log('❌ Call initiation failed!');
      console.log('   Error:', result.error);
      if (result.errorCode) {
        console.log('   Error Code:', result.errorCode);
        console.log('   More Info:', result.moreInfo);
      }
    }
    
  } catch (error) {
    console.error('💥 Unexpected error:', error);
  }
}

// Common Twilio error codes and their meanings
console.log('📚 Common Twilio Error Codes:');
console.log('   11200: HTTP retrieval failure (webhook URL unreachable)');
console.log('   11205: HTTP connection failure (webhook timeout)');
console.log('   21201: No international authorization (invalid phone number)');
console.log('   21211: Invalid "To" phone number');
console.log('   21212: Invalid "From" phone number');
console.log('   21214: "To" number is not a valid mobile number');
console.log('   21215: Account not authorized to call this number');
console.log('   21216: Account not authorized to call internationally');
console.log('   21217: Phone number not verified (trial account)');
console.log('   21218: The number is unverified (trial account)');
console.log('   21219: Trial account does not support this feature');
console.log('   21220: Invalid conference name');
console.log('   21221: Required parameter missing');
console.log('   21401: Invalid phone number format');
console.log('   21402: Invalid URL');
console.log('   21403: Forbidden');
console.log('   21404: The requested resource was not found');
console.log('   21405: Method not allowed');
console.log('   21408: Request timeout');
console.log('   21421: PhoneNumber Incapable of Receiving SMS');
console.log('   21601: Phone number is not a valid SMS-capable inbound phone number');
console.log('   21602: Message body is required');
console.log('   21603: The message cannot be sent to the number');
console.log('   21604: The number is not a valid mobile number');
console.log('   21605: The number is not currently reachable via SMS');
console.log('   21606: The number is not a valid phone number');
console.log('   21610: Message cannot be sent to the number');
console.log('   21611: This number has opted out of receiving messages');
console.log('   21612: The number is not currently reachable');
console.log('   21614: Message is not valid');
console.log('   21617: The number is not currently reachable via SMS');
console.log('   30001: Queue overflow');
console.log('   30002: Account suspended');
console.log('   30003: Unreachable destination handset');
console.log('   30004: Message blocked');
console.log('   30005: Unknown destination handset');
console.log('   30006: Landline or unreachable carrier');
console.log('   30007: Carrier violation');
console.log('   30008: Unknown error');
console.log('   30009: Missing segment');
console.log('   30010: Message price exceeds max price');
console.log('');

console.log('⚠️  IMPORTANT NOTES:');
console.log('   1. Make sure your webhook URLs are publicly accessible');
console.log('   2. For local development, use ngrok to expose your server');
console.log('   3. Trial accounts can only call verified numbers');
console.log('   4. Check that your phone numbers are in E.164 format (+**********)');
console.log('   5. Ensure your server is running and accessible');
console.log('');

console.log('🔧 To fix webhook issues:');
console.log('   1. Install ngrok: npm install -g ngrok');
console.log('   2. Start your server: npm start');
console.log('   3. In another terminal: ngrok http 5000');
console.log('   4. Copy the https URL from ngrok');
console.log('   5. Set WEBHOOK_BASE_URL environment variable');
console.log('   6. Or update the test configuration above');
console.log('');

// Uncomment the line below to run the test
// testCallInitiation();

console.log('💡 To run the test, uncomment the last line in this file and update TEST_PHONE_NUMBER');
