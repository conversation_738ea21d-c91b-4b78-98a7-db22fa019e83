# Call Initiation Timeout Troubleshooting Guide

## 🚨 Common Causes of Call Timeout Errors

### 1. **Webhook URL Issues (Most Common)**
**Problem**: <PERSON><PERSON><PERSON> cannot reach your webhook URLs during call setup.

**Symptoms**:
- Call initiation appears to start but times out
- No webhook callbacks received
- Twilio error codes: 11200, 11205, 21402

**Solutions**:
```bash
# Option A: Use ngrok for local development
npm install -g ngrok
npm start  # Start your server
ngrok http 5000  # In another terminal

# Copy the https URL (e.g., https://abc123.ngrok.io)
# Add to your .env file:
WEBHOOK_BASE_URL=https://abc123.ngrok.io
```

```bash
# Option B: Deploy to cloud service
# Heroku, AWS, DigitalOcean, etc.
```

### 2. **Trial Account Limitations**
**Problem**: Twilio trial accounts have restrictions.

**Symptoms**:
- Error codes: 21217, 21218, 21219
- "Phone number not verified" errors

**Solutions**:
- Verify phone numbers in Twilio Console
- Upgrade to paid account
- Only call verified numbers during trial

### 3. **Phone Number Format Issues**
**Problem**: Invalid phone number format.

**Symptoms**:
- Error codes: 21211, 21212, 21401
- "Invalid phone number" errors

**Solutions**:
- Use E.164 format: +**********
- Remove spaces, dashes, parentheses
- Include country code

### 4. **Network/Firewall Issues**
**Problem**: Network blocking Twilio requests.

**Symptoms**:
- Intermittent timeouts
- Connection refused errors

**Solutions**:
- Check firewall settings
- Ensure port 5000 is accessible
- Test with different network

### 5. **Server Configuration Issues**
**Problem**: Server not properly configured.

**Symptoms**:
- 404 errors on webhook URLs
- Server crashes during call

**Solutions**:
- Verify routes are properly configured
- Check server logs for errors
- Ensure all dependencies installed

## 🔧 Step-by-Step Debugging

### Step 1: Verify Twilio Configuration
```bash
node debug-twilio.js
```
Expected output: All checks should pass ✅

### Step 2: Test Call Initiation
```bash
# Edit test-call-initiation.js with a valid phone number
node test-call-initiation.js
```

### Step 3: Check Webhook Accessibility
```bash
# Test if your webhooks are accessible
curl -X POST http://localhost:5000/api/calls/status-callback
curl -X POST http://localhost:5000/api/calls/lead-connected
```

### Step 4: Monitor Server Logs
```bash
# Start server with verbose logging
npm start
# Watch for webhook calls and errors
```

## 🌐 Setting Up ngrok (Recommended)

1. **Install ngrok**:
```bash
npm install -g ngrok
```

2. **Start your server**:
```bash
npm start
```

3. **Create tunnel** (in new terminal):
```bash
ngrok http 5000
```

4. **Copy the HTTPS URL** from ngrok output:
```
Forwarding  https://abc123.ngrok.io -> http://localhost:5000
```

5. **Update environment variables**:
```bash
# Add to .env file
WEBHOOK_BASE_URL=https://abc123.ngrok.io
```

6. **Restart your server** to pick up new environment variable.

## 📱 Testing with Valid Phone Numbers

### For Trial Accounts:
1. Go to Twilio Console → Phone Numbers → Verified Caller IDs
2. Add and verify the phone number you want to call
3. Use only verified numbers for testing

### For Production:
- Any valid phone number should work
- Ensure international calling is enabled if needed

## 🔍 Common Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| 11200 | HTTP retrieval failure | Fix webhook URL |
| 11205 | HTTP connection failure | Check server accessibility |
| 21211 | Invalid "To" phone number | Fix phone number format |
| 21212 | Invalid "From" phone number | Check Twilio phone number |
| 21217 | Phone number not verified | Verify number in console |
| 21401 | Invalid phone number format | Use E.164 format |
| 21402 | Invalid URL | Fix webhook URL format |
| 21408 | Request timeout | Check server response time |

## 🚀 Quick Fix Checklist

- [ ] Server is running on port 5000
- [ ] ngrok tunnel is active (for local dev)
- [ ] WEBHOOK_BASE_URL is set correctly
- [ ] Phone numbers are in E.164 format (+**********)
- [ ] Phone numbers are verified (trial accounts)
- [ ] Twilio credentials are correct
- [ ] No firewall blocking requests
- [ ] Webhook endpoints return 200 status

## 📞 Test Call Flow

1. **Call Initiated**: POST /api/calls/initiate
2. **Twilio Creates Call**: Using provided TwiML
3. **Agent Hears Greeting**: "Hello Agent, connecting..."
4. **Twilio Dials Lead**: Using dial.number()
5. **Lead Answers**: Webhook called at /api/calls/lead-connected
6. **Parties Connected**: Both can talk
7. **Status Updates**: Sent to /api/calls/status-callback

## 🆘 Still Having Issues?

1. **Check Twilio Debugger**: https://console.twilio.com/us1/develop/voice/logs
2. **Review server logs** for detailed error messages
3. **Test with curl** to verify webhook accessibility
4. **Try different phone numbers** to isolate the issue
5. **Contact Twilio support** if using paid account

## 📝 Environment Variables Needed

```bash
# Required
TWILIO_ACCOUNT_SID=AC...
TWILIO_AUTH_TOKEN=...
TWILIO_PHONE_FROM=+**********

# For webhook URLs (development)
WEBHOOK_BASE_URL=https://your-ngrok-url.ngrok.io

# Optional
TWILIO_TWIML_APP_SID=AP...
```
