const sgMail = require('@sendgrid/mail');

// SendGrid configuration
const SENDGRID_CONFIG = {
  API_KEY: process.env.SENDGRID_API_KEY,
  FROM_EMAIL: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
  FROM_NAME: process.env.SENDGRID_FROM_NAME || 'Your Company'
};

// Initialize SendGrid with API key
if (SENDGRID_CONFIG.API_KEY) {
  sgMail.setApiKey(SENDGRID_CONFIG.API_KEY);
  console.log('✅ SendGrid initialized successfully');
} else {
  console.warn('⚠️ SendGrid API key not found. Email functionality will not work.');
}

// Email templates
const EMAIL_TEMPLATES = {
  WELCOME: {
    subject: 'Welcome to Our Services!',
    getHtml: (leadName) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Welcome ${leadName || 'there'}!</h1>
        </div>
        
        <div style="padding: 40px; background: #f8f9fa;">
          <p style="font-size: 16px; line-height: 1.6; color: #333;">
            Thank you for your interest in our services. We're excited to have you on board!
          </p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #667eea; margin-top: 0;">Here's what you can expect from us:</h3>
            <ul style="color: #555; line-height: 1.8;">
              <li>Personalized support</li>
              <li>Quality service delivery</li>
              <li>Regular updates</li>
              <li>24/7 customer service</li>
            </ul>
          </div>
          
          <p style="font-size: 16px; line-height: 1.6; color: #333;">
            If you have any questions, feel free to reach out. We're here to help!
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="mailto:${SENDGRID_CONFIG.FROM_EMAIL}" 
               style="background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Contact Us
            </a>
          </div>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p>Best regards,<br>Your Team at ${SENDGRID_CONFIG.FROM_NAME}</p>
        </div>
      </div>
    `,
    getText: (leadName) => `
      Welcome ${leadName || 'there'}!
      
      Thank you for your interest in our services. We're excited to have you on board!
      
      Here's what you can expect from us:
      • Personalized support
      • Quality service delivery
      • Regular updates
      • 24/7 customer service
      
      If you have any questions, feel free to reach out. We're here to help!
      
      Best regards,
      Your Team at ${SENDGRID_CONFIG.FROM_NAME}
    `
  },

  FOLLOW_UP: {
    subject: 'Following up on our conversation',
    getHtml: (leadName) => `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #2c3e50; padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px;">Follow-up</h1>
        </div>
        
        <div style="padding: 40px; background: #f8f9fa;">
          <p style="font-size: 16px; line-height: 1.6; color: #333;">
            Hi ${leadName || 'there'},
          </p>
          
          <p style="font-size: 16px; line-height: 1.6; color: #333;">
            I hope this email finds you well. I wanted to follow up on our recent conversation about our services.
          </p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3498db;">
            <p style="margin: 0; color: #555;">
              Is there anything specific you'd like to discuss or any questions you might have?
            </p>
          </div>
          
          <p style="font-size: 16px; line-height: 1.6; color: #333;">
            I'm here to help and would love to hear from you.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="mailto:${SENDGRID_CONFIG.FROM_EMAIL}" 
               style="background: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Reply to Us
            </a>
          </div>
        </div>
        
        <div style="background: #2c3e50; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p>Best regards,<br>Your Team</p>
        </div>
      </div>
    `,
    getText: (leadName) => `
      Hi ${leadName || 'there'},
      
      I hope this email finds you well. I wanted to follow up on our recent conversation about our services.
      
      Is there anything specific you'd like to discuss or any questions you might have?
      
      I'm here to help and would love to hear from you.
      
      Best regards,
      Your Team
    `
  }
};

// Helper function to send email
const sendEmail = async ({ to, subject, html, text, templateData = {} }) => {
  try {
    if (!SENDGRID_CONFIG.API_KEY) {
      throw new Error('SendGrid API key not configured');
    }

    const msg = {
      to: to,
      from: {
        email: SENDGRID_CONFIG.FROM_EMAIL,
        name: SENDGRID_CONFIG.FROM_NAME
      },
      subject: subject,
      text: text,
      html: html,
    };

    console.log('📧 Sending email via SendGrid:', {
      to: to,
      subject: subject,
      from: SENDGRID_CONFIG.FROM_EMAIL
    });

    const response = await sgMail.send(msg);
    
    console.log('✅ Email sent successfully via SendGrid');
    return {
      success: true,
      messageId: response[0].headers['x-message-id'],
      response: response[0]
    };

  } catch (error) {
    console.error('❌ SendGrid email error:', error);
    
    if (error.response) {
      console.error('❌ SendGrid error details:', error.response.body);
    }
    
    throw error;
  }
};

// Helper function to send template email
const sendTemplateEmail = async ({ to, templateType, leadName, customSubject, customData = {} }) => {
  try {
    const template = EMAIL_TEMPLATES[templateType];
    if (!template) {
      throw new Error(`Email template '${templateType}' not found`);
    }

    const subject = customSubject || template.subject;
    const html = template.getHtml(leadName, customData);
    const text = template.getText(leadName, customData);

    return await sendEmail({ to, subject, html, text });

  } catch (error) {
    console.error('❌ Template email error:', error);
    throw error;
  }
};

// Helper function to send custom email
const sendCustomEmail = async ({ to, subject, message, leadName }) => {
  try {
    // Create a simple HTML template for custom emails
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: #667eea; padding: 30px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 24px;">${subject}</h1>
        </div>
        
        <div style="padding: 40px; background: #f8f9fa;">
          <p style="font-size: 16px; line-height: 1.6; color: #333;">
            Hi ${leadName || 'there'},
          </p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <div style="white-space: pre-line; font-size: 16px; line-height: 1.6; color: #333;">
              ${message}
            </div>
          </div>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p>Best regards,<br>Your Team at ${SENDGRID_CONFIG.FROM_NAME}</p>
        </div>
      </div>
    `;

    const text = `Hi ${leadName || 'there'},\n\n${message}\n\nBest regards,\nYour Team at ${SENDGRID_CONFIG.FROM_NAME}`;

    return await sendEmail({ to, subject, html, text });

  } catch (error) {
    console.error('❌ Custom email error:', error);
    throw error;
  }
};

module.exports = {
  SENDGRID_CONFIG,
  EMAIL_TEMPLATES,
  sendEmail,
  sendTemplateEmail,
  sendCustomEmail
}; 