const express = require('express');
const router = express.Router();
const {
  initiateCallToLead,
  getCallStatusById,
  endCallById,
  getCallHistory,
  generateCallToken,
  handleLeadConnected,
  handleStatusCallback,
  handleRecordingStatus
} = require('../controllers/callController');

// POST /api/calls/initiate - Initiate call to lead using Twilio
router.post('/initiate', initiateCallToLead);

// POST /api/calls/token - Generate Twilio access token for client
router.post('/token', generateCallToken);

// GET /api/calls/:callSid/status - Get call status
router.get('/:callSid/status', getCallStatusById);

// DELETE /api/calls/:callSid - End call
router.delete('/:callSid', endCallById);

// GET /api/calls/lead/:leadId - Get call history for lead
router.get('/lead/:leadId', getCallHistory);

// Twilio webhook endpoints (public - no auth required)
// POST /api/calls/lead-connected - TwiML webhook when lead is connected
router.post('/lead-connected', handleLeadConnected);

// POST /api/calls/status-callback - Handle call status callbacks from Twilio
router.post('/status-callback', handleStatusCallback);

// POST /api/calls/recording-status - Handle recording status callbacks from Twilio
router.post('/recording-status', handleRecordingStatus);

module.exports = router; 