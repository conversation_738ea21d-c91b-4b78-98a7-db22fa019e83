# Database Configuration
MONGO_URI=mongodb://localhost:27017/leadgenapp
DB_NAME=leadgenapp

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_make_it_very_long_and_complex
JWT_EXPIRE=7d

# Server Configuration
PORT=5000
NODE_ENV=development

# Twilio Configuration (for WhatsApp and Voice calls)
TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_WHATSAPP_FROM=whatsapp:+***********
TWILIO_PHONE_FROM=+**********
TWILIO_TWIML_APP_SID=your_twiml_app_sid_here

# Optional Twilio API credentials (for enhanced security)
TWILIO_API_KEY=your_twilio_api_key_here
TWILIO_API_SECRET=your_twilio_api_secret_here

# Example values:
# TWILIO_ACCOUNT_SID=AC**********abcdef**********abcdef
# TWILIO_AUTH_TOKEN=your_auth_token_32_characters_long
# TWILIO_WHATSAPP_FROM=whatsapp:+*********** (Twilio Sandbox number)
# TWILIO_PHONE_FROM=+********** (Your verified Twilio phone number) 
# TWILIO_TWIML_APP_SID=AP**********abcdef**********abcdef (TwiML App SID for voice calls)
# TWILIO_API_KEY=SK**********abcdef**********abcdef (Optional: for access tokens)
# TWILIO_API_SECRET=your_api_secret_32_characters_long (Optional: for access tokens)

# SendGrid Configuration (for Email functionality)
SENDGRID_API_KEY=your_sendgrid_api_key_here
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Your Company Name

# Example values:
# SENDGRID_API_KEY=SG.**********abcdef.**********abcdef**********abcdef12345678
# SENDGRID_FROM_EMAIL=<EMAIL>
# SENDGRID_FROM_NAME=Your Company Support Team