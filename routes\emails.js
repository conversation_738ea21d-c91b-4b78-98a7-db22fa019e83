const express = require('express');
const router = express.Router();
const {
  getEmailTemplates,
  sendTemplateEmailToLead,
  sendCustomEmailToLead,
  getEmailHistory,
  testEmailConfiguration
} = require('../controllers/emailController');

// Middleware for authentication (if needed)
// const auth = require('../middleware/auth');

// GET /api/emails/templates - Get available email templates
router.get('/templates', getEmailTemplates);

// POST /api/emails/send-template - Send template email to lead
router.post('/send-template', sendTemplateEmailToLead);

// POST /api/emails/send-custom - Send custom email to lead
router.post('/send-custom', sendCustomEmailToLead);

// GET /api/emails/history/:leadId - Get email history for a lead
router.get('/history/:leadId', getEmailHistory);

// POST /api/emails/test - Test SendGrid configuration
router.post('/test', testEmailConfiguration);

module.exports = router; 