const twilio = require("twilio");

// Twilio Configuration
const TWILIO_CONFIG = {
  ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID,
  AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN,
  WHATSAPP_FROM: process.env.TWILIO_WHATSAPP_FROM, // Format: whatsapp:+**********
  PHONE_FROM: process.env.TWILIO_PHONE_FROM, // Your Twilio phone number
  TWIML_APP_SID: process.env.TWILIO_TWIML_APP_SID, // TwiML App SID for voice calls
  WEBHOOK_BASE_URL:
    process.env.TWILIO_WEBHOOK_BASE_URL || process.env.WEBHOOK_BASE_URL,
};

// Validate Twilio configuration
const validateTwilioConfig = () => {
  const requiredFields = ["ACCOUNT_SID", "AUTH_TOKEN", "PHONE_FROM"];
  const missingFields = requiredFields.filter((field) => !TWILIO_CONFIG[field]);

  if (missingFields.length > 0) {
    console.error(
      "❌ Missing required Twilio configuration fields:",
      missingFields
    );
    return false;
  }

  console.log("✅ Twilio configuration loaded successfully");
  return true;
};

// Initialize Twilio client
let twilioClient = null;

const initializeTwilioClient = () => {
  try {
    if (!validateTwilioConfig()) {
      throw new Error("Invalid Twilio configuration");
    }

    twilioClient = twilio(TWILIO_CONFIG.ACCOUNT_SID, TWILIO_CONFIG.AUTH_TOKEN);
    console.log("✅ Twilio client initialized successfully");
    return twilioClient;
  } catch (error) {
    console.error("❌ Failed to initialize Twilio client:", error.message);
    throw error;
  }
};

// Get Twilio client instance
const getTwilioClient = () => {
  if (!twilioClient) {
    return initializeTwilioClient();
  }
  return twilioClient;
};

// Format phone number for WhatsApp
const formatWhatsAppNumber = (phoneNumber) => {
  // Remove any existing whatsapp: prefix
  let cleanNumber = phoneNumber.replace(/^whatsapp:/, "");

  // Ensure number starts with +
  if (!cleanNumber.startsWith("+")) {
    cleanNumber = "+" + cleanNumber;
  }

  return `whatsapp:${cleanNumber}`;
};

// Format phone number for voice calls
const formatPhoneNumber = (phoneNumber) => {
  // Remove any non-numeric characters except +
  let cleanNumber = phoneNumber.replace(/[^\d+]/g, "");

  // Ensure number starts with +
  if (!cleanNumber.startsWith("+")) {
    cleanNumber = "+" + cleanNumber;
  }

  return cleanNumber;
};

// Generate TwiML for agent-to-lead calls
const generateAgentCallTwiML = (
  leadPhoneNumber,
  agentName = "Agent",
  webhookBaseUrl = ""
) => {
  const twiml = new twilio.twiml.VoiceResponse();

  // Say greeting to agent
  twiml.say(
    {
      voice: "alice",
      language: "en-US",
    },
    `Hello ${agentName}, connecting you to the lead. Please wait.`
  );

  // Dial the lead's number
  const dial = twiml.dial({
    timeout: 30,
    record: "record-from-answer",
    recordingStatusCallback: webhookBaseUrl
      ? `${webhookBaseUrl}/api/calls/recording-status`
      : "/api/calls/recording-status",
  });

  dial.number(
    {
      url: webhookBaseUrl
        ? `${webhookBaseUrl}/api/calls/lead-connected`
        : "/api/calls/lead-connected",
      method: "POST",
    },
    leadPhoneNumber
  );

  // If lead doesn't answer
  twiml.say(
    {
      voice: "alice",
      language: "en-US",
    },
    "The lead is not available at the moment. Please try again later."
  );

  return twiml.toString();
};

// Generate TwiML for when lead answers
const generateLeadConnectedTwiML = () => {
  const twiml = new twilio.twiml.VoiceResponse();

  // Greeting for the lead
  twiml.say(
    {
      voice: "alice",
      language: "en-US",
    },
    "Hello, you have an incoming call from our sales team. Please hold while we connect you."
  );

  // Connect both parties - this will allow them to talk to each other
  twiml.say(
    {
      voice: "alice",
      language: "en-US",
    },
    "You are now connected. You may start your conversation."
  );

  return twiml.toString();
};

// Initiate outbound call from agent to lead
const initiateCall = async (
  toNumber,
  fromNumber = TWILIO_CONFIG.PHONE_FROM,
  agentName = "Agent",
  webhookBaseUrl = ""
) => {
  try {
    const client = getTwilioClient();
    const formattedToNumber = formatPhoneNumber(toNumber);
    const formattedFromNumber = formatPhoneNumber(fromNumber);

    console.log(
      `📞 Initiating call from ${formattedFromNumber} to ${formattedToNumber}`
    );
    console.log(
      `🌐 Using webhook base URL: ${webhookBaseUrl || "relative URLs"}`
    );

    // Create the call
    const call = await client.calls.create({
      to: formattedToNumber,
      from: formattedFromNumber,
      twiml: generateAgentCallTwiML(
        formattedToNumber,
        agentName,
        webhookBaseUrl
      ),
      record: true,
      statusCallback: webhookBaseUrl
        ? `${webhookBaseUrl}/api/calls/status-callback`
        : "/api/calls/status-callback",
      statusCallbackMethod: "POST",
      statusCallbackEvent: ["initiated", "ringing", "answered", "completed"],
      timeout: 60,
    });

    console.log(`✅ Call initiated successfully. Call SID: ${call.sid}`);

    return {
      success: true,
      callSid: call.sid,
      status: call.status,
      to: call.to,
      from: call.from,
    };
  } catch (error) {
    console.error("❌ Twilio call initiation error:", error);
    console.error("❌ Error details:", {
      message: error.message,
      code: error.code,
      moreInfo: error.moreInfo,
      status: error.status,
    });
    return {
      success: false,
      error: error.message,
      errorCode: error.code,
      moreInfo: error.moreInfo,
    };
  }
};

// Get call status
const getCallStatus = async (callSid) => {
  try {
    const client = getTwilioClient();
    const call = await client.calls(callSid).fetch();

    return {
      success: true,
      status: call.status,
      duration: call.duration,
      startTime: call.startTime,
      endTime: call.endTime,
      direction: call.direction,
    };
  } catch (error) {
    console.error("❌ Twilio call status error:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

// End call
const endCall = async (callSid) => {
  try {
    const client = getTwilioClient();
    const call = await client.calls(callSid).update({
      status: "completed",
    });

    return {
      success: true,
      status: call.status,
      message: "Call ended successfully",
    };
  } catch (error) {
    console.error("❌ Twilio end call error:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

// Generate access token for Twilio Voice SDK
const generateAccessToken = (identity) => {
  try {
    const AccessToken = twilio.jwt.AccessToken;
    const VoiceGrant = AccessToken.VoiceGrant;

    // Create an access token which we will sign and return to the client
    const token = new AccessToken(
      TWILIO_CONFIG.ACCOUNT_SID,
      process.env.TWILIO_API_KEY || TWILIO_CONFIG.ACCOUNT_SID,
      process.env.TWILIO_API_SECRET || TWILIO_CONFIG.AUTH_TOKEN,
      { identity: identity }
    );

    // Grant access to Voice
    const voiceGrant = new VoiceGrant({
      outgoingApplicationSid: TWILIO_CONFIG.TWIML_APP_SID,
      incomingAllow: true,
    });

    token.addGrant(voiceGrant);

    return {
      success: true,
      token: token.toJwt(),
      identity: identity,
    };
  } catch (error) {
    console.error("❌ Twilio token generation error:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

module.exports = {
  TWILIO_CONFIG,
  getTwilioClient,
  initializeTwilioClient,
  validateTwilioConfig,
  formatWhatsAppNumber,
  formatPhoneNumber,
  generateAgentCallTwiML,
  generateLeadConnectedTwiML,
  initiateCall,
  getCallStatus,
  endCall,
  generateAccessToken,
};
