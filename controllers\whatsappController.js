const { getTwilioClient, TWILIO_CONFIG, formatWhatsAppNumber } = require('../config/twilio');
const Lead = require('../models/Lead');
const Message = require('../models/Message');

// Send WhatsApp message to lead
const sendWhatsAppMessage = async (req, res) => {
  try {
    const { leadId, message, messageType = 'text', agentId = null } = req.body;
    
    if (!leadId || !message) {
      return res.status(400).json({
        success: false,
        message: 'Lead ID and message are required'
      });
    }

    console.log('📱 Sending WhatsApp message to lead:', leadId);

    // Get lead details
    const lead = await Lead.findById(leadId);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    // Save message to database first
    const messageDoc = new Message({
      leadId: leadId,
      agentId: agentId,
      sender: 'agent',
      senderName: 'Agent', // You can get actual agent name from agentId
      messageType: messageType,
      content: message,
      direction: 'outbound',
      status: 'sending'
    });

    await messageDoc.save();

    // Get Twilio client
    const client = getTwilioClient();
    
    // Format phone numbers for WhatsApp
    const fromNumber = TWILIO_CONFIG.WHATSAPP_FROM;
    const toNumber = formatWhatsAppNumber(lead.phone);
    
    console.log('📱 WhatsApp details:', {
      leadName: lead.title,
      from: fromNumber,
      to: toNumber,
      messageLength: message.length
    });

    try {
      // Send WhatsApp message
      const twilioMessage = await client.messages.create({
        from: fromNumber,
        to: toNumber,
        body: message
      });

      // Update message with Twilio SID and status
      messageDoc.twilioSid = twilioMessage.sid;
      messageDoc.status = twilioMessage.status || 'sent';
      messageDoc.metadata = {
        twilioStatus: twilioMessage.status
      };
      await messageDoc.save();

      console.log('✅ WhatsApp message sent successfully:', twilioMessage.sid);

      // Emit real-time update to connected clients
      const io = req.app.get('io');
      if (io) {
        io.to(`lead_${leadId}`).emit('new_message', {
          _id: messageDoc._id,
          content: messageDoc.content,
          sender: messageDoc.sender,
          senderName: messageDoc.senderName,
          status: messageDoc.status,
          createdAt: messageDoc.createdAt,
          twilioSid: messageDoc.twilioSid
        });
      }

      res.json({
        success: true,
        data: {
          messageId: messageDoc._id,
          messageSid: twilioMessage.sid,
          status: twilioMessage.status,
          from: fromNumber,
          to: toNumber,
          messageBody: message,
          leadId: leadId,
          leadName: lead.title,
          sentAt: messageDoc.createdAt
        },
        message: 'WhatsApp message sent successfully'
      });

    } catch (twilioError) {
      // Update message status to failed
      messageDoc.status = 'failed';
      messageDoc.metadata = {
        errorCode: twilioError.code,
        errorMessage: twilioError.message
      };
      await messageDoc.save();
      
      throw twilioError;
    }

  } catch (error) {
    console.error('❌ Error sending WhatsApp message:', error);
    console.error('❌ Error details:', {
      message: error.message,
      code: error.code,
      moreInfo: error.moreInfo,
      status: error.status,
      details: error.details
    });
    
    // Handle specific Twilio errors
    if (error.code) {
      return res.status(400).json({
        success: false,
        message: `Twilio error: ${error.message}`,
        errorCode: error.code,
        moreInfo: error.moreInfo
      });
    }
    
    res.status(500).json({
      success: false,
      message: 'Failed to send WhatsApp message',
      error: error.message
    });
  }
};

// Get chat messages for a lead
const getChatMessages = async (req, res) => {
  try {
    const { leadId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    if (!leadId) {
      return res.status(400).json({
        success: false,
        message: 'Lead ID is required'
      });
    }

    // Get messages with pagination
    const messages = await Message.find({ leadId })
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate('leadId', 'title phone')
      .lean();

    // Reverse to show oldest first
    messages.reverse();

    // Get total count for pagination
    const total = await Message.countDocuments({ leadId });

    res.json({
      success: true,
      data: {
        messages,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(total / limit),
          totalMessages: total,
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1
        }
      }
    });

  } catch (error) {
    console.error('❌ Error fetching chat messages:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch chat messages',
      error: error.message
    });
  }
};

// Mark messages as read (Agent-side only - for internal tracking)
const markMessagesAsRead = async (req, res) => {
  try {
    const { leadId } = req.params;
    const { messageIds } = req.body;

    if (!leadId) {
      return res.status(400).json({
        success: false,
        message: 'Lead ID is required'
      });
    }

    console.log('📖 Marking messages as read for lead:', leadId);

    // Find incoming messages from lead that need to be marked as read (agent side only)
    const query = { leadId, sender: 'lead', status: { $ne: 'read' } };
    if (messageIds && messageIds.length > 0) {
      query._id = { $in: messageIds };
    }

    const unreadMessages = await Message.find(query);
    console.log(`📖 Found ${unreadMessages.length} unread messages from lead`);

    // Update local message status to read (this is for agent-side tracking only)
    const result = await Message.updateMany(
      query,
      { 
        $set: { 
          status: 'read', 
          readAt: new Date() 
        } 
      }
    );

    console.log(`📖 Marked ${result.modifiedCount} messages as read (agent-side)`);

    // NOTE: WhatsApp read receipts (blue ticks on sender's side) are automatically 
    // generated when the actual recipient opens WhatsApp and reads the message.
    // We cannot programmatically trigger these read receipts via Twilio API.
    // The blue ticks will appear on the lead's WhatsApp when they open and read your messages.

    // Emit real-time update
    const io = req.app.get('io');
    if (io) {
      io.to(`lead_${leadId}`).emit('messages_read', {
        leadId,
        readAt: new Date(),
        count: result.modifiedCount
      });
    }

    res.json({
      success: true,
      data: {
        updatedCount: result.modifiedCount,
        totalMessages: unreadMessages.length
      },
      message: `Messages marked as read (agent-side tracking)`
    });

  } catch (error) {
    console.error('❌ Error marking messages as read:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark messages as read',
      error: error.message
    });
  }
};

// Send template WhatsApp message (for opt-in purposes)
const sendTemplateMessage = async (req, res) => {
  try {
    const { leadId, templateName = 'hello_world' } = req.body;
    
    if (!leadId) {
      return res.status(400).json({
        success: false,
        message: 'Lead ID is required'
      });
    }

    // Get lead details
    const lead = await Lead.findById(leadId);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    const client = getTwilioClient();
    const fromNumber = TWILIO_CONFIG.WHATSAPP_FROM;
    const toNumber = formatWhatsAppNumber(lead.phone);

    // Send template message (this is typically used for initial opt-in)
    const twilioMessage = await client.messages.create({
      from: fromNumber,
      to: toNumber,
      contentSid: templateName, // Use your approved template SID
      contentVariables: JSON.stringify({
        "1": lead.title || "there" // Use 'title' field and provide fallback
      })
    });

    console.log('✅ WhatsApp template message sent:', twilioMessage.sid);

    res.json({
      success: true,
      data: {
        messageSid: twilioMessage.sid,
        status: twilioMessage.status,
        template: templateName,
        leadId: leadId
      },
      message: 'WhatsApp template message sent successfully'
    });

  } catch (error) {
    console.error('❌ Error sending WhatsApp template:', error);
    console.error('❌ Template error details:', {
      message: error.message,
      code: error.code,
      moreInfo: error.moreInfo
    });
    res.status(500).json({
      success: false,
      message: 'Failed to send WhatsApp template message',
      error: error.message,
      errorCode: error.code
    });
  }
};

// Get WhatsApp message status
const getMessageStatus = async (req, res) => {
  try {
    const { messageSid } = req.params;
    
    if (!messageSid) {
      return res.status(400).json({
        success: false,
        message: 'Message SID is required'
      });
    }

    const client = getTwilioClient();
    const message = await client.messages(messageSid).fetch();

    // Update local message status
    await Message.findOneAndUpdate(
      { twilioSid: messageSid },
      { 
        status: message.status,
        'metadata.twilioStatus': message.status,
        deliveredAt: message.status === 'delivered' ? new Date() : undefined
      }
    );

    res.json({
      success: true,
      data: {
        sid: message.sid,
        status: message.status,
        errorCode: message.errorCode,
        errorMessage: message.errorMessage,
        dateCreated: message.dateCreated,
        dateUpdated: message.dateUpdated,
        dateSent: message.dateSent
      }
    });

  } catch (error) {
    console.error('❌ Error fetching message status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch message status',
      error: error.message
    });
  }
};

// Enhanced webhook handler for WhatsApp message status updates and incoming messages
const handleWebhook = async (req, res) => {
  try {
    const { 
      MessageSid, 
      MessageStatus, 
      From, 
      To, 
      Body,
      MediaUrl0,
      MediaContentType0,
      NumMedia 
    } = req.body;
    
    console.log('📱 WhatsApp webhook received:', {
      messageSid: MessageSid,
      status: MessageStatus,
      from: From,
      to: To,
      body: Body,
      hasMedia: NumMedia > 0
    });

    // Handle status updates for outgoing messages
    if (MessageSid && MessageStatus) {
      console.log('🔄 Processing status update:', { MessageSid, MessageStatus });
      
      const updateData = { 
        status: MessageStatus,
        'metadata.twilioStatus': MessageStatus
      };

      // Set timestamp fields based on status
      if (MessageStatus === 'sent') {
        updateData.sentAt = new Date();
      } else if (MessageStatus === 'delivered') {
        updateData.deliveredAt = new Date();
      } else if (MessageStatus === 'read') {
        updateData.readAt = new Date();
      } else if (MessageStatus === 'failed' || MessageStatus === 'undelivered') {
        updateData.failedAt = new Date();
      }

      const message = await Message.findOneAndUpdate(
        { twilioSid: MessageSid },
        updateData,
        { new: true }
      );

      if (message) {
        console.log('✅ Status updated in database:', {
          messageId: message._id,
          oldStatus: message.status,
          newStatus: MessageStatus,
          leadId: message.leadId
        });

        // Emit real-time status update
        const io = req.app.get('io');
        if (io) {
          io.to(`lead_${message.leadId}`).emit('message_status_update', {
            messageId: message._id,
            twilioSid: MessageSid,
            status: MessageStatus,
            updatedAt: new Date()
          });
          
          console.log('📡 Emitted status update to room:', `lead_${message.leadId}`);
        }
      } else {
        console.warn('⚠️ Message not found for status update:', MessageSid);
      }
    }

    // Handle incoming messages from leads
    if (Body && From && From.includes('whatsapp:')) {
      const phoneNumber = From.replace('whatsapp:', '');
      
      // Enhanced lead matching strategy for duplicate phone numbers
      console.log('🔍 Looking for lead with phone number:', phoneNumber);
      
      // First, try to find a lead that has recent outbound messages (active conversation)
      const recentOutboundMessage = await Message.findOne({
        direction: 'outbound',
        createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
      }).populate('leadId').sort({ createdAt: -1 });

      let lead = null;
      
      if (recentOutboundMessage && recentOutboundMessage.leadId) {
        const recentLead = recentOutboundMessage.leadId;
        // Check if the recent lead's phone matches the incoming number
        if (recentLead.phone === phoneNumber || 
            recentLead.phone === `+${phoneNumber}` || 
            recentLead.phone.replace('+', '') === phoneNumber.replace('+', '')) {
          lead = recentLead;
          console.log('✅ Matched to lead with recent conversation:', lead.title, lead._id);
        }
      }
      
      // If no recent conversation match, fall back to finding any lead with the phone number
      if (!lead) {
        const allLeadsWithPhone = await Lead.find({ 
        phone: { $regex: phoneNumber.replace('+', '\\+'), $options: 'i' }
      });
        
        console.log(`🔍 Found ${allLeadsWithPhone.length} leads with phone ${phoneNumber}`);
        
        if (allLeadsWithPhone.length > 0) {
          // If multiple leads, prefer the most recently created one
          lead = allLeadsWithPhone.sort((a, b) => b.createdAt - a.createdAt)[0];
          
          if (allLeadsWithPhone.length > 1) {
            console.warn(`⚠️ Multiple leads found with phone ${phoneNumber}:`, 
              allLeadsWithPhone.map(l => `${l.title} (${l._id})`));
            console.log(`📌 Using most recent lead: ${lead.title} (${lead._id})`);
          }
        }
      }

      if (lead) {
        // Save incoming message
        const incomingMessage = new Message({
          leadId: lead._id,
          sender: 'lead',
          senderName: lead.title,
          messageType: NumMedia > 0 ? 'image' : 'text', // Simplified media detection
          content: Body || '[Media message]',
          direction: 'inbound',
          status: 'delivered',
          twilioSid: MessageSid,
          metadata: {
            mediaUrl: MediaUrl0,
            mediaContentType: MediaContentType0,
            twilioStatus: 'received'
          }
        });

        await incomingMessage.save();

        // Emit real-time update to connected agents
        const io = req.app.get('io');
        if (io) {
          io.to(`lead_${lead._id}`).emit('new_message', {
            _id: incomingMessage._id,
            content: incomingMessage.content,
            sender: incomingMessage.sender,
            senderName: incomingMessage.senderName,
            messageType: incomingMessage.messageType,
            status: incomingMessage.status,
            createdAt: incomingMessage.createdAt,
            metadata: incomingMessage.metadata
          });

          // Notify all agents about new lead message
          io.emit('lead_message_notification', {
            leadId: lead._id,
            leadName: lead.title,
            message: Body,
            timestamp: incomingMessage.createdAt
          });
        }

        console.log('✅ Incoming message saved:', incomingMessage._id);
      } else {
        console.warn('⚠️ Received message from unknown number:', phoneNumber);
        console.warn('💡 Consider creating a new lead or updating existing lead phone numbers to match exactly');
        
        // Log all leads for debugging
        const allLeads = await Lead.find({}, { title: 1, phone: 1 }).limit(10);
        console.log('📋 Sample leads in database:', allLeads.map(l => `${l.title}: ${l.phone}`));
      }
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('❌ Error handling WhatsApp webhook:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to handle webhook',
      error: error.message
    });
  }
};

module.exports = {
  sendWhatsAppMessage,
  getChatMessages,
  markMessagesAsRead,
  sendTemplateMessage,
  getMessageStatus,
  handleWebhook
}; 