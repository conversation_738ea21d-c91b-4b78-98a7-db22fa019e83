const Lead = require('../models/Lead');
const { initiateCall, getCallStatus, endCall, generateAccessToken, generateLeadConnectedTwiML } = require('../config/twilio');

// @desc    Initiate call to lead using Twilio
// @route   POST /api/calls/initiate
// @access  Private
const initiateCallToLead = async (req, res) => {
  try {
    const { leadId, agentName = 'Agent' } = req.body;
    
    console.log('📞 Initiating Twilio call to lead:', leadId);

    // Find the lead
    const lead = await Lead.findById(leadId);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    console.log('👤 Lead found:', lead.title, 'Phone:', lead.phone);

    // Validate phone number
    if (!lead.phone || lead.phone.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Lead phone number is required'
      });
    }

    // Format phone number (ensure it starts with +)
    let phoneNumber = lead.phone.trim();
    if (!phoneNumber.startsWith('+')) {
      phoneNumber = '+' + phoneNumber;
    }

    console.log('📱 Formatted phone number:', phoneNumber);

    // Initiate call using Twilio
    const callResult = await initiateCall(phoneNumber, undefined, agentName);
    
    if (!callResult.success) {
      console.error('❌ Call initiation failed:', callResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to initiate call',
        error: callResult.error
      });
    }

    console.log('✅ Call initiated successfully:', callResult.callSid);

    // Update lead status to contacted if it was new
    if (lead.status === 'new') {
      lead.status = 'contacted';
      await lead.save();
      console.log('📝 Lead status updated to contacted');
    }

    res.status(200).json({
      success: true,
      message: 'Call initiated successfully',
      data: {
        callSid: callResult.callSid,
        status: callResult.status,
        leadId: lead._id,
        leadName: lead.title,
        phoneNumber: phoneNumber,
        to: callResult.to,
        from: callResult.from
      }
    });

  } catch (error) {
    console.error('💥 Call initiation error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during call initiation'
    });
  }
};

// @desc    Get call status using Twilio
// @route   GET /api/calls/:callSid/status
// @access  Private
const getCallStatusById = async (req, res) => {
  try {
    const { callSid } = req.params;
    
    console.log('📊 Checking Twilio call status for:', callSid);

    const statusResult = await getCallStatus(callSid);
    
    if (!statusResult.success) {
      console.error('❌ Failed to get call status:', statusResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get call status',
        error: statusResult.error
      });
    }

    console.log('✅ Call status retrieved:', statusResult.status);

    res.status(200).json({
      success: true,
      data: {
        callSid,
        status: statusResult.status,
        duration: statusResult.duration,
        startTime: statusResult.startTime,
        endTime: statusResult.endTime,
        direction: statusResult.direction
      }
    });

  } catch (error) {
    console.error('💥 Get call status error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while getting call status'
    });
  }
};

// @desc    End call using Twilio
// @route   DELETE /api/calls/:callSid
// @access  Private
const endCallById = async (req, res) => {
  try {
    const { callSid } = req.params;
    
    console.log('🔚 Ending Twilio call:', callSid);

    const endResult = await endCall(callSid);
    
    if (!endResult.success) {
      console.error('❌ Failed to end call:', endResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to end call',
        error: endResult.error
      });
    }

    console.log('✅ Call ended successfully');

    res.status(200).json({
      success: true,
      message: 'Call ended successfully',
      data: {
        callSid,
        status: endResult.status
      }
    });

  } catch (error) {
    console.error('💥 End call error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while ending call'
    });
  }
};

// @desc    Get call history for a lead
// @route   GET /api/calls/lead/:leadId
// @access  Private
const getCallHistory = async (req, res) => {
  try {
    const { leadId } = req.params;
    
    console.log('📋 Getting call history for lead:', leadId);

    // Find the lead
    const lead = await Lead.findById(leadId);
    if (!lead) {
      return res.status(404).json({
        success: false,
        message: 'Lead not found'
      });
    }

    // In a real implementation, you would store call history in a separate collection
    // For now, we'll return basic lead info
    res.status(200).json({
      success: true,
      data: {
        leadId: lead._id,
        leadName: lead.title,
        phoneNumber: lead.phone,
        status: lead.status,
        lastContacted: lead.updatedAt
      }
    });

  } catch (error) {
    console.error('💥 Get call history error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while getting call history'
    });
  }
};

// @desc    Generate Twilio access token for client
// @route   POST /api/calls/token
// @access  Private
const generateCallToken = async (req, res) => {
  try {
    const { identity } = req.body;
    
    if (!identity) {
      return res.status(400).json({
        success: false,
        message: 'Identity is required'
      });
    }

    console.log('🔐 Generating Twilio access token for:', identity);

    const tokenResult = generateAccessToken(identity);
    
    if (!tokenResult.success) {
      console.error('❌ Token generation failed:', tokenResult.error);
      return res.status(500).json({
        success: false,
        message: 'Failed to generate access token',
        error: tokenResult.error
      });
    }

    console.log('✅ Twilio access token generated successfully');

    res.json({
      success: true,
      data: {
        token: tokenResult.token,
        identity: tokenResult.identity
      }
    });

  } catch (error) {
    console.error('❌ Error generating Twilio token:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate token'
    });
  }
};

// @desc    TwiML webhook for when lead is connected
// @route   POST /api/calls/lead-connected
// @access  Public (Twilio webhook)
const handleLeadConnected = (req, res) => {
  try {
    console.log('📞 Lead connected webhook called');
    const twiml = generateLeadConnectedTwiML();
    res.type('text/xml');
    res.send(twiml);
  } catch (error) {
    console.error('❌ Error in lead connected webhook:', error);
    res.status(500).send('Error processing webhook');
  }
};

// @desc    Handle call status callbacks from Twilio
// @route   POST /api/calls/status-callback
// @access  Public (Twilio webhook)
const handleStatusCallback = (req, res) => {
  try {
    const { CallSid, CallStatus, Duration, From, To } = req.body;
    
    console.log(`📞 Call status callback: ${CallSid} - ${CallStatus}`);
    console.log(`📞 Call details: From ${From} to ${To}, Duration: ${Duration}`);
    
    // Here you could update your database with call status
    // For example, save call logs, update lead status, etc.
    
    res.status(200).send('OK');
  } catch (error) {
    console.error('❌ Error in status callback:', error);
    res.status(500).send('Error processing callback');
  }
};

// @desc    Handle recording status callbacks from Twilio
// @route   POST /api/calls/recording-status
// @access  Public (Twilio webhook)
const handleRecordingStatus = (req, res) => {
  try {
    const { CallSid, RecordingSid, RecordingUrl, RecordingStatus } = req.body;
    
    console.log(`🎙️ Recording status: ${RecordingSid} - ${RecordingStatus}`);
    console.log(`🎙️ Recording URL: ${RecordingUrl}`);
    
    // Here you could save the recording URL to your database
    // For example, associate it with the lead or call log
    
    res.status(200).send('OK');
  } catch (error) {
    console.error('❌ Error in recording status callback:', error);
    res.status(500).send('Error processing callback');
  }
};

module.exports = {
  initiateCallToLead,
  getCallStatusById,
  endCallById,
  getCallHistory,
  generateCallToken,
  handleLeadConnected,
  handleStatusCallback,
  handleRecordingStatus
}; 