{"name": "leadgen-backend", "version": "1.0.0", "description": "Backend server for Lead Generation App", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-api.js", "test-whatsapp": "node test-whatsapp-api.js", "seed": "node scripts/seedData.js", "check-db": "node check-database.js", "seed-leads": "node scripts/seedLeads.js"}, "keywords": ["lead-generation", "nodejs", "mongodb", "express"], "author": "Your Name", "license": "MIT", "dependencies": {"@sendgrid/mail": "^8.1.5", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "node-fetch": "^2.7.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "twilio": "^5.7.1"}, "devDependencies": {"nodemon": "^3.0.2"}}